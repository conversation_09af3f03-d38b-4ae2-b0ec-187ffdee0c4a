<template>
  <div>
    <xyy-dialog ref="dialogTable" title="异常提交" width="700px" @on-close="closeDialog">
      <!-- table 组件 -->
      <vxe-table
        ref="abnormalTable"
        highlight-current-row
        highlight-hover-row
        height="500"
        class="abnormal-table"
        :loading="loading"
        :data="abnormalData"
      >
        <vxe-table-column type="seq" title="序号" width="80" />
        <vxe-table-column field="realPickingNumber" title="实际数量" width="120" />
        <vxe-table-column field="reviewNumber" title="复核数量" width="120" />
        <vxe-table-column field="exceptionCause" title="错误类型" width="120">
          <template v-slot="{row}">
            <el-select
              v-model="row.exceptionCause"
            >
              <el-option
                v-for="item in optCause"
                :key="item.value"
                clearable
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column field="exceptionReason" title="错误原因" width="140">
          <template v-slot="{row}">
            <el-select
              v-model="row.exceptionReason"
            >
              <el-option
                v-for="item in optRemark"
                :key="item.value"
                clearable
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column field="exceptionNumber" title="异常数量" width="120">
          <template v-slot="{row}">
            <el-input
              v-model.number="row.exceptionNumber"
              maxlength="8"
              text="number"
              @input.native="handlerChange($event, row)"
            />
          </template>
        </vxe-table-column>
        <vxe-table-column field="remark" title="备注留言" width="120" show-overflow="title">
          <template v-slot="{row}">
            <el-input
              v-model.number="row.remark"
              maxlength="200"
              :title="row.remark"
            />
          </template>
        </vxe-table-column>
        <vxe-table-column field="productCode" title="商品编码" width="120" />
        <vxe-table-column field="batchNumber" title="批号" width="90" />
        <vxe-table-column field="goodsAllocation" title="货位" width="90" show-overflow="title" />
        <vxe-table-column field="sterilizingBatchNumber" title="灭菌批号" width="120" />
        <vxe-table-column field="passBoxCode" title="小车号" width="90" />
        <vxe-table-column field="packingUnit" title="包装单位" width="120" />
        <vxe-table-column field="producingArea" title="产地" width="90" />
      </vxe-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="cancel()">取消</el-button>
        <el-button type="primary" @click="submit()">确认</el-button>
      </span>
    </xyy-dialog>
  </div>
</template>

<script>
export default {
  name: 'SubmitAbnormal',
  data() {
    return {
      show: false,
      loading: false, // 列表懒加载
      abnormalData: [], // 异常数据
      rowData: null, // 原表格数据
      rowDataIndex: null, // 原数据索引
      exceptionCause: '1', // 错误类型
      exceptionReason: '1', // 错误原因
      exceptionNumber: null, // 异常数量
      optCause: [
        {
          value: '1',
          label: '少货'
        },
        {
          value: '2',
          label: '多货'
        }
      ],
      optRemark: [
        {
          value: '1',
          label: '商品漏发'
        },
        {
          value: '2',
          label: '商品发错'
        },
        {
          value: '3',
          label: '批号发错'
        },
        {
          value: '4',
          label: '商品多发'
        },
        {
          value: '5',
          label: '错拣'
        }
      ]
    }
  },
  computed: {
  },
  mounted() {
  },
  created() {
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$refs.dialogTable.close()
      this.show = false
    },
    // 禁止空白关闭弹窗
    closeDialog() {
      this.$emit('on-before-close')
    },
    // 打开弹窗
    open(rowData) {
      this.show = true
      const list = JSON.parse(JSON.stringify(rowData))
      list.forEach(element => {
        element.exceptionCause = this.exceptionCause
        element.exceptionReason = this.exceptionReason
        element.exceptionNumber = ''
      })
      // 缓存弹窗表格打开时需要填充的初始数据
      this.abnormalData = list
      this.$refs.dialogTable.open()
    },
    check(e, row) {
      // 只允许输入正整数
      e.target.value = e.target.value.replace(/^(0+)|[^\d]+/g, '').replace('.', '')
      const tem = Object.assign({}, row)
      // 异常数量、实际数量\件包装数量
      const { exceptionNumber, specification } = tem
      // 当库别为整件库时，异常数量需是件包装的整数倍。
      if (parseInt(specification) > 1) {
        // const num = parseInt(exceptionNumber)
        // let returnValue = 0
        if (exceptionNumber % specification === 0) {
          return
        } else {
          // var i = Math.floor(num / specification)

          // if ((num - i * specification) > 18) {
          //   e.target.value = (i + 1) * specification
          // } else {
          //   e.target.value = i * specification
          // }
          this.$message.warning(exceptionNumber + '不是件包装的倍数,建议输入' + specification)
          // return
        }
      }
    },
    // 异常数量input校验
    handlerChange(e, row) {
      // 只允许输入正整数
      e.target.value = e.target.value.replace(/^(0+)|[^\d]+/g, '').replace('.', '')
      const tem = Object.assign({}, row)
      // 异常数量、实际数量\件包装数量
      const { exceptionNumber, realPickingNumber } = tem
      // 如果异常大于复核,提示
      if (parseInt(exceptionNumber) > parseInt(realPickingNumber)) {
        this.$message.warning('异常数量应小于实际数量!')
        e.target.value = ''
        return
      }
      // console.log('==specificationspecification==', specification)
    },
    // 取消按钮
    cancel() {
      this.close()
    },
    // 确认按钮
    submit() {
      // console.log('===abnormalData---', this.abnormalData)
      const rowData = JSON.parse(JSON.stringify(this.abnormalData))
      const abnormalList = []
      let flag = true
      rowData.forEach(item => {
        if (item.exceptionNumber === '') {
          this.$message.warning('请输入异常数量!')
          flag = false
          return
        }
        // 当库别为整件库时，异常数量需是件包装的整数倍。
        if (parseInt(item.specification) > 1) {
          const num = parseInt(item.exceptionNumber)
          if (num % item.specification !== 0) {
            flag = false
            this.$message.warning(num + '不是件包装的倍数,建议输入' + item.specification)
          }
        }
        // 异常商品数量
        abnormalList.push(item)
      })
      if (flag) {
        this.close()
        // 确认时保存当前异常数据
        this.$emit('abnormal', abnormalList)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
