<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@ybm100.com
 * @Descripttion: 异常提交弹窗
 * @Date: 2022-05-30 10:20:21
-->
<template>
    <div>
      <xyy-dialog
        ref="dialogTableVisible"
        title="商品列表"
        width="40%"
        :visible.sync="outerVisible"
      >
        <!-- table 组件 -->
        <vxe-table
          ref="exceptionSubmissionTable"
          highlight-current-row
          highlight-hover-row
          height="300px"
          class="exception-submission-table"
          :loading="loading"
          :data="goodsData"
          @cell-dblclick="handleCellClick"
        >
          <!-- :columns="tableColumn" -->
          <vxe-table-column type="seq" title="序号" width="80" />
          <vxe-table-column field="productCode" title="商品编码" width="200" />
          <vxe-table-column field="productName" title="商品名称" width="200" />
          <vxe-table-column field="batchNumber" title="批号" width="245" />
        </vxe-table>
      </xyy-dialog>
    </div>
  </template>
  
  <script>
import { getExceptionGoodsList } from '@/api/outstock/fhdb'
  export default {
    name: "goodsDialog",
    data() {
      return {
        show: false,
        loading: false, // 列表懒加载
        goodsData: [], // 商品数据
        outerVisible: false,
      };
    },
    methods: {
      // 关闭弹窗
      close() {
        this.$refs.dialogTableVisible.close();
      },
      // 打开弹窗
      open(params) {
        this.loading = true;
        //获取数据
        getExceptionGoodsList({packageBarCode:params[1]}).then(res => {
          this.loading = false
          const { code, msg, result } = res
          //二维数组转一维数组
          const singleArray = [];
          if (params[0].length>=0) {
            params[0].forEach( e => {
              let itemObj = {
                batchNumber: e.batchNumber,
                productCode: e.productCode
              }
              singleArray.push(itemObj)
            })
          }
          if (code === 0) {
            //数据过滤
            if(result.length>0){
              //对比返回的数据和已选的数据 ，已选的数据不在返回的数据中显示
              let localData = result.filter(item => !singleArray.some(data => (data.productCode === item.productCode && data.batchNumber === item.batchNumber)))
              this.goodsData = localData
            }
          }
        })
        this.$refs.dialogTableVisible.open();
      },
      unique(arr) {
        return arr.filter(function(item, index, arr) {
          //当前元素，在原始数组中的第一个索引==当前索引值，否则返回当前元素
          return arr.indexOf(item, 0) === index;
        });
      },
      //点击当前行
      handleCellClick(row, event, column) {
        //改变数据
        let rowChanged = Object.assign(row.row, {"isResultGoods": true},{"exceptionCause":"2"})
        //发送事件给父组件
        this.$emit('checkGoodsData', rowChanged)
        //删除该条数据
        this.goodsData.splice(this.goodsData.indexOf(row.row),1)
        //关闭
        this.$refs.dialogTableVisible.close()
      },
      //重新回写从异常列表删除的数据
      overWriteGoodsData(params){
        if(params.length>=0){
          this.goodsData = []
          params.forEach(e=>{
            this.goodsData.push(e)
          }) 
        }
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  </style>
  