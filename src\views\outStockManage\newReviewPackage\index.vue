<template>
  <div class="new-review-package-container" v-loading="mainLoading">
    <!-- 顶部操作区域 -->
    <div class="top-section">
      <!-- 销售单号输入区域 -->
      <div class="sales-order-section">
        <div class="input-group">
          <label class="input-label">销售单号</label>
          <el-input
            ref="erpOrderCode"
            v-model="formData.erpOrderCode"
            placeholder="请输入销售单号或扫描条码，或者扫描面单"
            class="sales-order-input"
            onfocus="this.select()"
            @keyup.enter.native="handleSalesOrderEnter"
          />
          <el-button type="primary" @click="handleQueryTask">查看任务</el-button>
          <el-button type="warning" @click="handleRefresh">刷新</el-button>
        </div>

        <!-- 统计信息 -->
        <div class="stats-info">
          <div class="stat-item">
            <span class="stat-label">随货同行数量:</span>
            <span class="stat-value">{{ statisticsData.accompanyingCount || 2 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">未打:</span>
            <span class="stat-value unprinted">{{ statisticsData.unprintedCount || 1 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">快递类型:</span>
            <span class="stat-value">{{ deliveryInfo.type || '顺丰(易碎损)' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">订单类型:</span>
            <span class="stat-value">{{ deliveryInfo.orderType || '京东' }}</span>
          </div>
        </div>
      </div>

      <!-- 耗材码输入区域 -->
      <div class="material-code-section">
        <div class="input-group">
          <label class="input-label">耗材码</label>
          <el-input
            ref="materialCode"
            v-model="formData.materialCode"
            placeholder="请扫描耗材码"
            class="material-code-input"
            onfocus="this.select()"
            @keyup.enter.native="handleMaterialCodeEnter"
          />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧商品图片区域 -->
      <div class="left-section">
        <div class="product-image-container">
          <div class="image-placeholder">
            <el-image
              :src="currentProduct.productImage"
              fit="contain"
              class="product-image"
            >
              <div slot="placeholder" class="image-slot">
                <span>商品图片</span>
              </div>
            </el-image>
          </div>
        </div>

        <!-- 商品基本信息 -->
        <div class="product-basic-info">
          <div class="info-row">
            <span class="info-label">商品条码:</span>
            <span class="info-value">{{ currentProduct.productCode || '69010193485576' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">商品名称:</span>
            <span class="info-value">{{ currentProduct.productName || '三九感冒灵颗粒 10袋/盒' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">商品编号:</span>
            <span class="info-value">{{ currentProduct.productNumber || 'Y1003020' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">包装单位:</span>
            <span class="info-value">{{ currentProduct.packingUnit || '盒' }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧商品列表区域 -->
      <div class="right-section">
        <!-- 商品列表表格 -->
        <div class="product-table-container">
          <vxe-table
            ref="productTable"
            :data="productList"
            height="300"
            stripe
            border
            :cell-class-name="getCellClassName"
            @cell-click="handleCellClick"
          >
            <vxe-table-column field="productCode" title="商品条码" width="120" />
            <vxe-table-column field="productName" title="商品名称" width="150" />
            <vxe-table-column field="productNumber" title="商品编号" width="100" />
            <vxe-table-column field="productCodeDisplay" title="商品编码(仅显示后6位)" width="150" />
            <vxe-table-column field="packedCount" title="已装数" width="80" />
            <vxe-table-column field="targetCount" title="未装数" width="80" />
            <vxe-table-column field="totalCount" title="总数数" width="80" />
          </vxe-table>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <el-button type="primary" @click="handlePackageConfirm">包装确认</el-button>
          <el-button type="danger" @click="handleExceptionSubmit">异常提交</el-button>
          <el-button type="info" @click="handleUpdateTransport">更换运输</el-button>
        </div>

        <!-- 统计信息区域 -->
        <div class="package-stats">
          <div class="stats-row">
            <div class="stat-group">
              <span class="stat-title">已装箱商品总数</span>
              <span class="stat-number">{{ packageStats.packedTotal || 10 }}</span>
            </div>
            <div class="stat-group">
              <span class="stat-title">未装箱商品总数</span>
              <span class="stat-number unpackaged">{{ packageStats.unpackedTotal || 250 }}</span>
            </div>
            <div class="stat-group">
              <span class="stat-title">商品总数</span>
              <span class="stat-number">{{ packageStats.grandTotal || 260 }}</span>
            </div>
          </div>
        </div>

        <!-- 底部详细信息表格 -->
        <div class="detail-table-container">
          <vxe-table
            ref="detailTable"
            :data="detailList"
            height="150"
            stripe
            border
          >
            <vxe-table-column field="productCode" title="商品条码" width="120" />
            <vxe-table-column field="productName" title="商品名称" width="150" />
            <vxe-table-column field="packedQuantity" title="已装箱数量" width="100" />
            <vxe-table-column field="unpackedQuantity" title="未装箱数量" width="100" />
            <vxe-table-column field="productNumber" title="商品编号" width="100" />
            <vxe-table-column field="productGroup" title="商品组别" width="100" />
          </vxe-table>
        </div>

        <!-- 扫描输入区域 -->
        <div class="scan-input-section">
          <div class="scan-input-group">
            <div class="input-item">
              <label class="scan-label">耗材编码</label>
              <el-input
                v-model="scanData.materialCode"
                placeholder="H001"
                class="scan-input"
              />
            </div>
            <div class="input-item">
              <label class="scan-label">耗材名称</label>
              <el-input
                v-model="scanData.materialName"
                placeholder="二手箱"
                class="scan-input"
              />
            </div>
            <div class="input-item">
              <label class="scan-label">操作</label>
              <el-button type="text" @click="handleDeleteOperation">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复用原有的弹窗组件 -->
    <!-- 追溯码扫描弹窗 -->
    <tracing-code-scan
      ref="tracingCodeScanDialog"
      @on-before-close="closeDialog"
      @regulatoryCodeScanBack="handleTracingCodeScanBack"
    />

    <!-- 查看任务弹窗 -->
    <view-tasks
      ref="viewTasksDialog"
      @on-before-close="closeViewTasksDialog"
    />

    <!-- 异常提交弹窗 -->
    <exception-submission
      ref="exceptionSubmitDialog"
      @on-before-close="closeExceptionSubmitDialog"
      @inputOpenDialog="openGoodsDialog"
      @deleteRow="deleteRow"
    />

    <!-- 商品列表弹窗 -->
    <goods-dialog
      ref="goodsDialog"
      @checkGoodsData="checkGoodsData"
    />

    <!-- 承运商更换弹窗 -->
    <changecys
      ref="changecys"
      @back-data="changecysBackData"
    />

    <!-- 承运商更换确认弹窗 -->
    <changeCYSAlertNew
      ref="dialogChangeCYSAlert"
    />

    <!-- 编辑箱码弹窗 -->
    <editBox
      ref="editBox"
    />
  </div>
</template>

<script>
import { doTTS } from "@/utils/tts.js";
import tracingCodeScan from "../reviewPackage/components/tracingCodeScan"; // 追溯码扫描
import editBox from "../reviewPackage/components/editBoxCodeWindow.vue";
import changecys from "../reviewPackage/components/changeCYSAlert.vue";
import changeCYSAlertNew from "../reviewPackage/components/changeCYSAlertNew.vue";
import ViewTasks from "../reviewPackage/components/viewTasks"; // 查看任务
import ExceptionSubmission from "../reviewPackage/components/exceptionSubmission"; // 异常提交
import GoodsDialog from "../reviewPackage/components/goodsDialog"; // 商品列表弹窗
import { modifyConsolidation } from "@/api/outstock/distribution";
import {
  getPartsInReviewGoodsSalesInfo,
  listPartsInReviewGoodsView,
  review,
  reviewConfirm,
  videoStart,
  videoPing,
  getMergeOrderList,
  saveBoxCode,
  checkBoxCode,
  getPartsInReviewTask,
  getPartsInReviewGoodsInfo,
  getPartsInReviewGoodsInfoByOrderCode,
  getPartsInReviewGoodsInfoByMergeOrderCode,
  getPartsInReviewGoodsInfoByAllocationCode,
  getPartsInReviewGoodsInfoByProductCode,
  getPartsInReviewGoodsInfoByProductName,
  getPartsInReviewGoodsInfoByProductNumber,
  getPartsInReviewGoodsInfoByPackingUnit,
  getPartsInReviewGoodsInfoByReviewStatus,
  getPartsInReviewGoodsInfoByWhetherRegulatory,
  getPartsInReviewGoodsInfoByReviewNumber,
  getPartsInReviewGoodsInfoByAllocationNumber,
  getPartsInReviewGoodsInfoByProductImage,
  getPartsInReviewGoodsInfoByProductCodeDisplay,
  getPartsInReviewGoodsInfoByPackedCount,
  getPartsInReviewGoodsInfoByTargetCount,
  getPartsInReviewGoodsInfoByTotalCount,
  getPartsInReviewGoodsInfoByPackedQuantity,
  getPartsInReviewGoodsInfoByUnpackedQuantity,
  getPartsInReviewGoodsInfoByProductGroup
} from "@/api/stockOutOnline/fhdbToC";
import XEUtils from "xe-utils"; // 表格组件工具
import axios from "axios";

export default {
  name: "NewReviewPackage",
  components: {
    ViewTasks,
    tracingCodeScan,
    editBox,
    changecys,
    changeCYSAlertNew,
    ExceptionSubmission,
    GoodsDialog
  },
  data() {
    return {
      // 主要加载状态
      mainLoading: false,
      loading: false,
      hasDialog: false,

      // 表单数据
      formData: {
        erpOrderCode: '', // 销售单号
        materialCode: '', // 耗材码
        orderCode: '',
        allocationCode: '',
        mergeOrderCode: ''
      },

      // 当前商品信息
      currentProduct: {
        productCode: '',
        productName: '',
        productNumber: '',
        packingUnit: '',
        productImage: ''
      },

      // 统计数据
      statisticsData: {
        accompanyingCount: 2, // 随货同行数量
        unprintedCount: 1 // 未打数量
      },

      // 快递信息
      deliveryInfo: {
        type: '顺丰(易碎损)', // 快递类型
        orderType: '京东' // 订单类型
      },

      // 商品列表数据
      productList: [
        {
          productCode: '69010193485576',
          productName: '三九感冒灵颗粒 10袋/盒',
          productNumber: 'Y1003020',
          productCodeDisplay: '250501(20)',
          packedCount: 0,
          targetCount: 20,
          totalCount: 20
        }
      ],

      // 包装统计
      packageStats: {
        packedTotal: 10, // 已装箱商品总数
        unpackedTotal: 250, // 未装箱商品总数
        grandTotal: 260 // 商品总数
      },

      // 详细信息列表
      detailList: [],

      // 扫描数据
      scanData: {
        materialCode: 'H001',
        materialName: '二手箱'
      },

      // 耗材表格数据
      boxTableData: [],

      // 快捷键列表
      btnList: [
        {
          label: "查看任务",
          type: "primary",
          shortkey: "F4",
          clickEvent: this.handleQueryTask,
          code: 'btn:wms:newReviewPackage:search'
        },
        {
          label: "包装确认",
          type: "success",
          shortkey: "F5",
          clickEvent: this.handlePackageConfirm,
          code: 'btn:wms:newReviewPackage:confirm'
        },
        {
          label: "异常提交",
          type: "danger",
          shortkey: "F6",
          clickEvent: this.handleExceptionSubmit,
          code: 'btn:wms:newReviewPackage:exception'
        },
        {
          label: "更换运输",
          type: "warning",
          shortkey: "F7",
          clickEvent: this.handleUpdateTransport,
          code: 'btn:wms:newReviewPackage:transport'
        },
        {
          label: "刷新",
          type: "info",
          shortkey: "F8",
          clickEvent: this.handleRefresh,
          code: 'btn:wms:newReviewPackage:refresh'
        }
      ]
    };
  },

  computed: {
    // 获取用户信息
    storageTool() {
      return this.$storageTool;
    }
  },

  mounted() {
    this.initPage();
    this.bindKeyboardEvents();
  },

  beforeDestroy() {
    this.unbindKeyboardEvents();
  },

  methods: {
    // 初始化页面
    initPage() {
      this.$nextTick(() => {
        this.$refs.erpOrderCode.focus();
      });
    },

    // 绑定键盘事件
    bindKeyboardEvents() {
      document.addEventListener('keydown', this.handleKeyDown);
    },

    // 解绑键盘事件
    unbindKeyboardEvents() {
      document.removeEventListener('keydown', this.handleKeyDown);
    },

    // 处理键盘事件
    handleKeyDown(event) {
      if (this.hasDialog) return; // 如果有弹窗打开，不处理快捷键

      switch (event.key) {
        case 'F4':
          event.preventDefault();
          this.handleQueryTask();
          break;
        case 'F5':
          event.preventDefault();
          this.handlePackageConfirm();
          break;
        case 'F6':
          event.preventDefault();
          this.handleExceptionSubmit();
          break;
        case 'F7':
          event.preventDefault();
          this.handleUpdateTransport();
          break;
        case 'F8':
          event.preventDefault();
          this.handleRefresh();
          break;
      }
    },

    // 销售单号回车事件
    handleSalesOrderEnter() {
      if (!this.formData.erpOrderCode.trim()) {
        this.$message.warning('请输入销售单号');
        return;
      }
      this.apiGetPartsInReviewGoodsSalesInfo();
    },

    // 耗材码回车事件
    handleMaterialCodeEnter() {
      if (!this.formData.materialCode.trim()) {
        this.$message.warning('请输入耗材码');
        return;
      }
      this.apiCheckBoxCode();
    },

    // 查看任务
    handleQueryTask() {
      this.$refs.viewTasksDialog.open();
      this.hasDialog = true;
    },

    // 刷新
    handleRefresh() {
      this.clearPage();
      this.$refs.erpOrderCode.focus();
    },

    // 包装确认
    handlePackageConfirm() {
      if (this.boxTableData.length === 0) {
        this.$message.error("请先输入耗材码后再进行操作");
        return;
      }
      this.apiReviewConfirm();
    },

    // 异常提交
    handleExceptionSubmit() {
      this.$refs.exceptionSubmitDialog.open({
        orderCode: this.formData.orderCode,
        allocationCode: this.formData.allocationCode,
        mergeOrderCode: this.formData.mergeOrderCode
      });
      this.hasDialog = true;
    },

    // 更换运输
    handleUpdateTransport() {
      this.$refs.changecys.open({
        orderCode: this.formData.orderCode,
        allocationCode: this.formData.allocationCode
      });
      this.hasDialog = true;
    },

    // 表格单元格点击事件
    handleCellClick({ row, column }) {
      if (column.field === 'productCode' || column.field === 'productName') {
        this.selectProduct(row);
      }
    },

    // 选择商品
    selectProduct(product) {
      this.currentProduct = { ...product };
      this.reviewClick(product);
    },

    // 复核点击事件
    reviewClick(row) {
      const { reviewStatus, whetherRegulatory } = row;
      if (reviewStatus === 2) {
        this.unReview(row);
      } else if (whetherRegulatory === 1 && row.reviewNumber != 0) {
        this.whetherRegulatory(row); // 是否展示电子监管码弹窗
      } else {
        this.checkAndPosition1(row); // 选中相同商品复核（相同编码）
      }
    },

    // 取消复核
    unReview(row) {
      this.apiReview(row.allocationCode, row.productCode, 1, row.orderCode);
    },

    // 是否需要监管码
    whetherRegulatory(row) {
      this.$refs.tracingCodeScanDialog.open(row);
      this.hasDialog = true;
    },

    // 选中相同商品复核
    checkAndPosition1(row) {
      this.apiReview(row.allocationCode, row.productCode, 2, row.orderCode);
    },

    // 获取单元格样式类名
    getCellClassName({ row, column }) {
      if (row.reviewStatus === 2) {
        return 'reviewed-cell';
      }
      if (row.reviewStatus === 1) {
        return 'reviewing-cell';
      }
      return '';
    },

    // 删除操作
    handleDeleteOperation() {
      if (this.scanData.materialCode) {
        this.deleteBox({ boxCode: this.scanData.materialCode });
      }
    },

    // 删除耗材
    deleteBox(row) {
      const index = this.boxTableData.findIndex(item => item.boxCode === row.boxCode);
      if (index > -1) {
        this.boxTableData.splice(index, 1);
        this.$message.success('删除成功');
      }
    },

    // 清空页面数据
    clearPage() {
      this.formData = {
        erpOrderCode: '',
        materialCode: '',
        orderCode: '',
        allocationCode: '',
        mergeOrderCode: ''
      };
      this.currentProduct = {
        productCode: '',
        productName: '',
        productNumber: '',
        packingUnit: '',
        productImage: ''
      };
      this.productList = [];
      this.detailList = [];
      this.boxTableData = [];
      this.packageStats = {
        packedTotal: 0,
        unpackedTotal: 0,
        grandTotal: 0
      };
    },

    // 弹窗关闭事件处理
    closeDialog() {
      this.hasDialog = false;
    },

    closeViewTasksDialog() {
      this.hasDialog = false;
      this.$refs.erpOrderCode.focus();
    },

    closeExceptionSubmitDialog() {
      this.hasDialog = false;
      this.$refs.erpOrderCode.focus();
      this.apiListPartsInReviewGoodsView();
    },

    // 追溯码扫描回调
    handleTracingCodeScanBack(result) {
      this.setRightNumber(result);
      this.apiListPartsInReviewGoodsView();
    },

    // 商品弹窗相关
    openGoodsDialog(data) {
      this.$refs.goodsDialog.open(data);
    },

    checkGoodsData(data) {
      // 处理商品数据
      console.log('商品数据:', data);
    },

    deleteRow(data) {
      // 删除行数据
      console.log('删除行:', data);
    },

    // 承运商更换回调
    changecysBackData(data) {
      console.log('承运商更换数据:', data);
    },

    // 设置右侧数据
    setRightNumber(result) {
      if (result && result.data) {
        // 更新统计数据
        this.packageStats = {
          packedTotal: result.data.packedTotal || 0,
          unpackedTotal: result.data.unpackedTotal || 0,
          grandTotal: result.data.grandTotal || 0
        };
      }
    },

    // TTS语音播报
    dottsFunction(text) {
      if (text && typeof doTTS === 'function') {
        doTTS(text);
      }
    },

    //-------------------------------API方法-----------------------------

    // 生成ID
    genID() {
      const length = 10;
      return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
    },

    // 获取销售单信息
    apiGetPartsInReviewGoodsSalesInfo() {
      this.mainLoading = true;
      getPartsInReviewGoodsSalesInfo({
        erpOrderCode: this.formData.erpOrderCode
      }).then((res) => {
        this.mainLoading = false;
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.formData.orderCode = result.orderCode;
          this.formData.allocationCode = result.allocationCode;
          this.formData.mergeOrderCode = result.mergeOrderCode;

          // 更新快递信息
          this.deliveryInfo = {
            type: result.deliveryType || '顺丰(易碎损)',
            orderType: result.orderType || '京东'
          };

          // 获取商品列表
          this.apiListPartsInReviewGoodsView();

          // 焦点转移到耗材码输入框
          this.$nextTick(() => {
            this.$refs.materialCode.focus();
          });
        } else {
          this.$message.error(msg || '获取销售单信息失败');
        }
      }).catch(() => {
        this.mainLoading = false;
        this.$message.error('获取销售单信息失败');
      });
    },

    // 获取复核商品列表
    apiListPartsInReviewGoodsView() {
      this.loading = true;
      listPartsInReviewGoodsView({
        orderCode: this.formData.orderCode,
        allocationCode: this.formData.allocationCode,
        mergeOrderCode: this.formData.mergeOrderCode,
      }).then((res) => {
        this.loading = false;
        const { code, msg, result } = res;
        if (code === 0 && result) {
          const data = JSON.parse(JSON.stringify(result));
          data.forEach(item => {
            item.rowId = this.genID();
            // 处理商品编码显示（仅显示后6位）
            if (item.productCode && item.productCode.length > 6) {
              item.productCodeDisplay = item.productCode.substr(-6);
            } else {
              item.productCodeDisplay = item.productCode;
            }
          });
          this.productList = data;

          // 如果有数据，默认选中第一个
          if (data.length > 0) {
            this.currentProduct = { ...data[0] };
          }

          // 更新统计数据
          this.updatePackageStats();
        } else {
          this.$message.error(msg || '获取商品列表失败');
        }
      }).catch(() => {
        this.loading = false;
        this.$message.error('获取商品列表失败');
      });
    },

    // 商品复核
    apiReview(allocationCode, productCode, reviewType, orderCode) {
      const params = {
        allocationCode,
        productCode,
        reviewType, // 1: 取消复核, 2: 复核
        orderCode,
        mergeOrderCode: this.formData.mergeOrderCode
      };

      review(params).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          this.$message.success(msg || '操作成功');
          this.setRightNumber(result);
          this.apiListPartsInReviewGoodsView(); // 刷新列表

          // 语音播报
          if (reviewType === 2) {
            const product = this.productList.find(item => item.productCode === productCode);
            if (product) {
              this.dottsFunction(product.reviewNumber + product.packingUnit);
            }
          }
        } else {
          this.$message.error(msg || '操作失败');
        }
      }).catch(() => {
        this.$message.error('操作失败');
      });
    },

    // 复核确认
    apiReviewConfirm() {
      this.mainLoading = true;
      reviewConfirm({
        orderCode: this.formData.orderCode,
        allocationCode: this.formData.allocationCode,
        mergeOrderCode: this.formData.mergeOrderCode,
        boxCodes: this.boxTableData.map(item => item.boxCode)
      }).then((res) => {
        this.mainLoading = false;
        const { code, msg } = res;
        if (code === 0) {
          this.$message.success(msg || '包装确认成功');
          this.clearPage();
          this.$refs.erpOrderCode.focus();
        } else {
          this.$message.error(msg || '包装确认失败');
        }
      }).catch(() => {
        this.mainLoading = false;
        this.$message.error('包装确认失败');
      });
    },

    // 检查耗材码
    apiCheckBoxCode() {
      checkBoxCode({
        boxCode: this.formData.materialCode,
        orderCode: this.formData.orderCode
      }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          // 添加到耗材列表
          const existingBox = this.boxTableData.find(item => item.boxCode === this.formData.materialCode);
          if (!existingBox) {
            this.boxTableData.push({
              boxCode: this.formData.materialCode,
              boxType: result.boxType || '二手箱'
            });
            this.scanData.materialCode = this.formData.materialCode;
            this.scanData.materialName = result.boxType || '二手箱';
          }

          this.formData.materialCode = '';
          this.$message.success(msg || '耗材码验证成功');

          // 焦点回到耗材码输入框
          this.$nextTick(() => {
            this.$refs.materialCode.focus();
          });
        } else {
          this.$message.error(msg || '耗材码验证失败');
        }
      }).catch(() => {
        this.$message.error('耗材码验证失败');
      });
    },

    // 更新包装统计
    updatePackageStats() {
      let packedTotal = 0;
      let unpackedTotal = 0;
      let grandTotal = 0;

      this.productList.forEach(item => {
        packedTotal += item.packedCount || 0;
        unpackedTotal += item.targetCount || 0;
        grandTotal += item.totalCount || 0;
      });

      this.packageStats = {
        packedTotal,
        unpackedTotal,
        grandTotal
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.new-review-package-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .top-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .sales-order-section {
      margin-bottom: 20px;

      .input-group {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;

        .input-label {
          font-weight: bold;
          min-width: 80px;
          color: #333;
        }

        .sales-order-input {
          flex: 1;
          max-width: 400px;
        }
      }

      .stats-info {
        display: flex;
        gap: 30px;

        .stat-item {
          display: flex;
          align-items: center;

          .stat-label {
            color: #666;
            margin-right: 5px;
          }

          .stat-value {
            font-weight: bold;
            color: #333;

            &.unprinted {
              color: #f56c6c;
            }
          }
        }
      }
    }

    .material-code-section {
      .input-group {
        display: flex;
        align-items: center;
        gap: 10px;

        .input-label {
          font-weight: bold;
          min-width: 80px;
          color: #333;
        }

        .material-code-input {
          max-width: 300px;
        }
      }
    }
  }

  .main-content {
    display: flex;
    gap: 20px;

    .left-section {
      width: 400px;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .product-image-container {
        margin-bottom: 20px;

        .image-placeholder {
          width: 100%;
          height: 300px;
          border: 2px dashed #ddd;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8px;

          .product-image {
            width: 100%;
            height: 100%;
          }

          .image-slot {
            color: #999;
            font-size: 16px;
          }
        }
      }

      .product-basic-info {
        .info-row {
          display: flex;
          margin-bottom: 10px;

          .info-label {
            min-width: 80px;
            color: #666;
            font-weight: bold;
          }

          .info-value {
            color: #333;
            flex: 1;
          }
        }
      }
    }

    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .product-table-container {
        margin-bottom: 20px;
      }

      .action-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        justify-content: center;
      }

      .package-stats {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;

        .stats-row {
          display: flex;
          justify-content: space-around;

          .stat-group {
            text-align: center;

            .stat-title {
              display: block;
              color: #666;
              font-size: 14px;
              margin-bottom: 5px;
            }

            .stat-number {
              display: block;
              font-size: 24px;
              font-weight: bold;
              color: #333;

              &.unpackaged {
                color: #f56c6c;
              }
            }
          }
        }
      }

      .detail-table-container {
        margin-bottom: 20px;
      }

      .scan-input-section {
        .scan-input-group {
          display: flex;
          gap: 15px;
          align-items: end;

          .input-item {
            flex: 1;

            .scan-label {
              display: block;
              margin-bottom: 5px;
              color: #666;
              font-weight: bold;
            }

            .scan-input {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

// 表格样式
::v-deep .vxe-table {
  .reviewed-cell {
    background-color: #f0f9ff !important;
    color: #1890ff !important;
  }

  .reviewing-cell {
    background-color: #fff7e6 !important;
    color: #fa8c16 !important;
  }
}
</style>