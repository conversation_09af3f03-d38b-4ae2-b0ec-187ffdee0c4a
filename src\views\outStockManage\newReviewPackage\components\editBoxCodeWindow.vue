<template>
  <xyy-dialog
    ref="dialogTableVisible"
    :title="'修改箱码'"
    width="400px"
    @on-close="onClose"
  >
    <div style="height:100px;">
      <el-form
        v-model="formData"
        :label-width="'120px'"
      >
        <el-col
          :lg="24"
          :md="24"
        >
          <el-form-item label="拼箱号">
            <el-select
              v-model="formData.consolidationCode"
              @change="changeF"
            >
              <el-option
                v-for="item in consolidationCodes"
                :label="item.consolidationCode"
                :value="item.consolidationCode"
                :key="item.consolidationCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col
          :lg="24"
          :md="24"
        >
          <el-form-item label="包装箱型">
            <el-input v-model="formData.boxCode"></el-input>
          </el-form-item>
        </el-col>
        <el-col
          :lg="24"
          :md="24"
        >
          <el-form-item label="类别名称">
            <div>{{formData.boxType}}</div>
          </el-form-item>
        </el-col>
      </el-form>
    </div>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="primary"
        @click="deleteBtnClick"
      >删除拼箱</el-button>
      <el-button @click="cancelBtnClick">取消</el-button>
      <el-button
        type="primary"
        @click="sureBtnClick"
      >确定</el-button>
    </span>
  </xyy-dialog>
</template>

<script>
import {
  getConsolidation,
  deleteConsolidation,
  modifyConsolidation,
} from "@/api/outstock/distribution.js";
export default {
  data() {
    return {
      formData: {
        consolidationCode: "",
        boxCode: "",
        boxType: "",
      },
      mergeOrderCode: "",
      consolidationCodes: [],
    };
  },
  methods: {
    open(mergeOrderCode) {
      this.$refs.dialogTableVisible.open();
      this.mergeOrderCode = mergeOrderCode;
      this.init();
      this.apigetConsolidation(mergeOrderCode);
    },
    changeF(val) {
      let item = this.consolidationCodes.find((item) => {
        return item.consolidationCode === val;
      });
  
      if (item) {
        this.formData.boxCode = item.boxCode;
        this.formData.boxType = item.boxType;
      }
    },
    onClose() {},
    cancelBtnClick() {
      this.$refs.dialogTableVisible.close();
    },
    sureBtnClick() {
      let params = {
        mergeOrderCode: this.mergeOrderCode,
        boxCode: this.formData.boxCode,
        consolidationCode: this.formData.consolidationCode,
      };
      this.apimodifyConsolidation(params);
    },
    deleteBtnClick() {
      let params = {
        mergeOrderCode: this.mergeOrderCode,
        consolidationCode: this.formData.consolidationCode,
      };
      this.apideleteConsolidation(params);
    },
    // ---------------api----------------------------
    apigetConsolidation(mergeOrderCode) {
      getConsolidation({ mergeOrderCode: mergeOrderCode }).then((res) => {
        const { code, msg, result } = res;
        if (code !== 0) {
          this.$message.error(msg);
        } else {
          if (result.length === 0) {
            this.init();
            this.$message.error("请先新增");
            this.$refs.dialogTableVisible.close();
            return;
          }
          this.consolidationCodes = result;
          this.formData.consolidationCode = result[0].consolidationCode;
          this.changeF(result[0].consolidationCode);
        }
      });
    },
    apideleteConsolidation(params) {
      deleteConsolidation(params).then((res) => {
        const { code, msg, result } = res;
        if (code !== 0) {
          this.$message.error(msg);
        } else {
          this.$message.success("删除成功");
          this.init();
          this.apigetConsolidation(this.mergeOrderCode);
        }
      });
    },
    apimodifyConsolidation(params) {
      modifyConsolidation(params).then((res) => {
        const { code, msg, result } = res;
        if (code !== 0) {
          this.$message.error(msg);
          return;
        }
        this.$message.success("修改成功");
        this.$refs.dialogTableVisible.close();
        this.$emit("done");
      });
    },
    //-------------------------tool-----------------
    init() {
      this.consolidationCodes = [];
      this.formData = {
        consolidationCode: "",
        boxCode: "",
        boxType: "",
      };
    },
  },
};
</script>

<style scoped>
</style>