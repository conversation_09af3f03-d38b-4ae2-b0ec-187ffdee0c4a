<!--
 * @Author: z<PERSON><PERSON><PERSON><EMAIL>
 * @Descripttion: 异常提交弹窗
 * @Date: 2022-05-30 10:20:21
-->
<template>
  <div>
    <xyy-dialog
      ref="dialogTableVisible"
      title="异常提交"
      width="80%"
      :visible.sync="outerVisible"
      @on-close="closeDialog"
    >
      <!-- table 组件 -->
      <vxe-table
        ref="exceptionSubmissionTable"
        highlight-current-row
        highlight-hover-row
        height="500px"
        class="exception-submission-table"
        :loading="loading"
        :data="exceptionSubmissionData"
        :pager-config="tablePage"
        @page-change="handlePageChange"
        @cell-click="handleCellClick"
      >
        <!-- :columns="tableColumn" -->
        <vxe-table-column type="checkbox" width="40" />
        <vxe-table-column type="seq" title="序号" width="80" />
        <vxe-table-column field="productCode" title="商品编码" width="120" />
        <vxe-table-column field="productName" title="商品名称" width="120" />
        <vxe-table-column field="packingUnit" title="包装单位" width="120" />
        <vxe-table-column field="specifications" title="规格" width="120" />
        <vxe-table-column field="producingArea" title="产地" width="90" />
        <vxe-table-column field="batchNumber" title="批号" width="90" />
        <vxe-table-column
          field="sterilizingBatchNumber"
          title="灭菌批号"
          width="120"
        />
        <vxe-table-column field="passBoxNum" title="周转箱" width="200" />
        <vxe-table-column field="reviewDeskNo" title="复核台" width="120" />
        <vxe-table-column field="manufacturer" title="生产厂家" width="150" />
        <vxe-table-column
          field="realPickingNumber"
          title="实际数量"
          width="120"
        />
        <vxe-table-column field="reviewNumber" title="复核数量" width="120" />
        <vxe-table-column field="exceptionCause" title="错误类型" width="120">
          <template v-slot="{ row }">
            <el-select v-model="row.exceptionCause">
              <el-option
                v-for="item in optCause"
                :key="item.value"
                clearable
                :label="item.label"
                :value="item.value"
                :disabled="row.isResultGoods && item.value==='1' ? true : false"
              />
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column field="exceptionReason" title="错误原因" width="140">
          <template v-slot="{ row }">
            <el-select v-model="row.exceptionReason">
              <el-option
                v-for="item in optRemark"
                :key="item.value"
                clearable
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column field="exceptionNumber" title="异常数量" width="120">
          <template v-slot="{ row }">
            <el-input
              v-model.number="row.exceptionNumber"
              maxlength="8"
              text="number"
            />
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="remark"
          title="备注留言"
          width="120"
          show-overflow="title"
        >
          <template v-slot="{ row }">
            <el-input
              v-model="row.remark"
              maxlength="200"
              :title="row.remark"
            />
          </template>
        </vxe-table-column>
      </vxe-table>
      <span
        class="footer"
        style="
          width: 100%;
          display: flex;
          justify-content: end;
          margin-top: 20px;
        "
      >
        <el-row type="flex" justify="space-between" style="width: 100%;">
          <el-col :span="12">
              <span style="margin-right:5px;">异常商品扫描：</span>
              <vxe-input
                  v-model="goodsCode"
                  placeholder="请输入名称"
                  clearable
                  @keydown.enter.native="inputOpenDialog"
                ></vxe-input>
          </el-col>
          <el-col :span="12">
            <el-row type="flex" justify="end" >
              <vxe-button status="primary" @click="deleteRow">删除行</vxe-button>
              <vxe-button status="primary" @click="close">取消</vxe-button>
              <vxe-button status="primary" @click="submit">确定</vxe-button>
            </el-row>
          </el-col>
        </el-row>
      </span>
    </xyy-dialog>
  </div>
</template>

<script>
import { submitException } from "@/api/outstock/distribution";
export default {
  name: "exceptionSubmission",
  data() {
    return {
      show: false,
      loading: false, // 列表懒加载
      exceptionCause: "1", // 错误类型
      exceptionReason: "1", // 错误原因
      exceptionNumber: null, // 异常数量
      goodsCode:"", //商品编码
      optCause: [
        {
          value: "1",
          label: "少货",
        },
        {
          value: "2",
          label: "多货",
        },
      ],
      optRemark: [
        {
          value: "1",
          label: "商品漏发",
        },
        {
          value: "2",
          label: "商品发错",
        },
        {
          value: "3",
          label: "批号发错",
        },
        {
          value: "4",
          label: "商品多发",
        },
        {
          value: "5",
          label: "错拣",
        },
      ],
      exceptionSubmissionData: [], // 任务数据
      tablePage: {
        total: 0,
        pageNum: 1,
        pageSize: 100,
      },
      outerVisible: false,
      innerVisible: true,
      mergeOrderCode: ""  //合单单号
    };
  },
  methods: {
    //打开商品列表弹窗
    inputOpenDialog(){
      this.$emit("inputOpenDialog", this.exceptionSubmissionData, this.goodsCode)
    },
    // 分页
    handlePageChange({ pageNum, pageSize }) {
      this.tablePage.pageNum = pageNum;
      this.tablePage.pageSize = pageSize;
      // this.findList()//列表
    },
    // 关闭弹窗
    close() {
      this.$refs.dialogTableVisible.close();
    },
    // 删除行
    deleteRow() {
      const deletedData = [];
      const rowData = this.$refs.exceptionSubmissionTable.getCheckboxRecords();
      if (rowData.length > 0) {
        rowData.forEach((item,i) => {
          const index = this.exceptionSubmissionData.indexOf(item)
          if(index>=0){
            this.exceptionSubmissionData.splice(index,1)
            deletedData.push(item)
          }
        });
      }
      this.$emit("deleteRow",deletedData);
    },
    // 禁止空白关闭弹窗
    closeDialog() {
      this.$emit("on-before-close");
    },
    // 接收商品列表数据
    receiveGoodsData(params) {
      let flag = false
      this.exceptionSubmissionData.forEach((e)=>{
        //修改类型options
        if(e.batchNumber===params.batchNumber && e.productCode===params.productCode){
          this.$message.warning("该数据已选，请勿选择重复数据！")
          flag = true
        }
      });
      if (!flag) {
        this.exceptionSubmissionData.push(params)
      }
    },
    // 打开弹窗
    open(rowData,mergeOrderCode) {
      this.mergeOrderCode = mergeOrderCode
      this.show = true;
      const list = JSON.parse(JSON.stringify(rowData));
      list.forEach((element) => {
        element.exceptionCause = this.exceptionCause;
        element.exceptionReason = this.exceptionReason;
        element.exceptionNumber = "";
      });
      // 缓存弹窗表格打开时需要填充的初始数据
      this.exceptionSubmissionData = list;
      this.$refs.dialogTableVisible.open();
    },
    // 异常提交
    submit() {
      const rowData = this.$refs.exceptionSubmissionTable.getCheckboxRecords();
      if (rowData.length > 0) {
        const params = [];
        rowData.forEach((item) => {
          var param = {
            mergeOrderCode: this.mergeOrderCode, // 合单单号
            productCode: item.productCode, // 商品编码
            batchNumber: item.batchNumber, // 批号
            exceptionNumber: item.exceptionNumber, // 异常数量
            exceptionCause: item.exceptionCause, // 错误类型：1-少货；2-多货
            exceptionReason: item.exceptionReason, // 错误原因：1-商品漏发；2-商品发错；3-批号发错；4-商品多发；5-错拣
            productName: item.productName, // 商品名称
            manufacturer: item.manufacturer, // 生产厂家
            remark: item.remark, // 备注留言
            specifications: item.specifications, // 规格
          };
          params.push(param);
        });
        submitException(params).then((res) => {
          const { code, msg, result } = res;
          if (code === 0) {
            this.$message.success("上传异常成功");
            this.close();
            this.$emit("on-before-close");
          } else {
            this.$message.error(msg);
          }
        });
      } else {
        this.$message.warning("请选择需要异常提交的商品!");
      }
    },
    handleCellClick(row, event, column) {
      this.$refs.exceptionSubmissionTable.setCheckboxRow(row.row, true);
    }
  },
};
</script>
<style lang="scss" scoped>
</style>
