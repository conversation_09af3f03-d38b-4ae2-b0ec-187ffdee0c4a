<template>
    <div>
      <xyy-dialog title="请输入解锁账号" ref="unLockDialog" width="648px">
        <el-form ref="formData" :model="formData" label-width="120px" :rules="rules" class="clearfix" autocomplete="off">
            <el-form-item label="用户名" prop="staffNum"  style="padding-bottom: 10px;">
                <el-input v-model="formData.staffNum" readonly @focus="$event.target.removeAttribute('readonly')"/>
            </el-form-item>
            <el-form-item label="密码" prop="password">
                <el-input v-model="formData.password" show-password readonly @focus="$event.target.removeAttribute('readonly')"/>
            </el-form-item>
        </el-form>
            <div style="display: flex;justify-content: flex-end;">
                <el-button type="info" @click="cancelHandler"
                v-if="getPermission('btn:wms:reviewPackage:unlock:cancel')">取消</el-button>
                <el-button type="primary"  @click="confirm" 
                v-if="getPermission('btn:wms:reviewPackage:unlock:confirm')">确定</el-button>
            </div>
        </xyy-dialog>
    </div>
</template>
<script>
import { loginReview } from '@/api/buyExit/exitPreview.js'
export default {
    name: 'unLockDialog',
    data(){
        return {
            formData: {
                staffNum: "",
                password: "",
                resourceCode: "outstock:review:inReviewUnlock"
            },
            rules: {
                staffNum: [
                    { required: true, message: '工号必填', trigger: 'blur' },
                ],
                password: [
                    { required: true, message: '密码必填', trigger: 'blur' },
                ]
            }
        }
    },
    methods: {
        open() {
            this.$refs.unLockDialog.open()
        },
        // 关闭弹框清空数据
        close() {
            this.$refs.unLockDialog.close()
        },
        // 按钮权限校验
        getPermission(code) {
            if (!this.$route.meta.buttonList) {
                return false;
            }
            const permissions = this.$route.meta.buttonList.map((item) => {
                return item.code;
            });
            return permissions.indexOf(code) !== -1;
        },
        confirm() {
            this.$refs.formData.validate((valid) => {
                if (valid) {
                    this.loading = true
                    const params = this.formData
                    loginReview(params).then(res => {
                        this.loading = false
                        const { code, msg, result } = res
                        if (code === 0) {
                            this.close()
                            this.$refs["formData"].resetFields();
                            this.$emit('on-close', result)  
                        } else {
                            this.$message.error(msg)
                        }
                    })
                } else {
                    return false
                }
            })
        },
        cancelHandler(){
            this.$refs["formData"].resetFields();
            this.close()
        }
    }
}
</script>