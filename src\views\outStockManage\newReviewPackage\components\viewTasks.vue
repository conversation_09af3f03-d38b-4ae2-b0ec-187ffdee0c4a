<template>
  <div>
    <xyy-dialog ref="dialogTableVisible" title="查看任务" width="900px" :visible.sync="outerVisible" @on-close="closeDialog">
      <!-- table 组件 -->
      <vxe-table
        ref="tasksTable"
        highlight-current-row
        highlight-hover-row
        height="500"
        class="tasks-table"
        :loading="loading"
        :data="tasksData"
        :pager-config="tablePage"
        @page-change="handlePageChange"
      >
        <!-- :columns="tableColumn" -->
        <vxe-table-column title="操作" width="120" show-overflow>
          <template v-slot="{ row }">
            <vxe-button title="查看明细" @click="riewRowEvent(row)">查看明细</vxe-button>
          </template>
        </vxe-table-column>
        <vxe-table-column field="orderCode" title="出库单号" width="200" />
        <vxe-table-column field="batchInspectionCode" title="批拣单号" width="200" />
        <vxe-table-column field="allocationCode" title="分配单号" width="200" />
        <vxe-table-column field="isAskFor" title="是否索取" width="90" />
        <vxe-table-column field="taskStatus" title="复核状态" width="90" />
        <vxe-table-column field="erpOrderCode" title="销售单号" width="90" />
        <vxe-table-column field="distributionPositionCode" title="分播位" width="90" />
        <vxe-table-column field="reviewStageName" title="复核员" width="90" />
      </vxe-table>
      <div class="pager">
        <vxe-pager
          border
          :current-page="tablePage.pageNum"
          :page-size="tablePage.pageSize"
          :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange"
        />
      </div>
    </xyy-dialog>
    <!-- 任务明细弹窗 -->
    <detail-view-tasks ref="detailDialog" :visible.sync="innerVisible" @on-before-close="closedetailDialog" />

  </div>
</template>

<script>
import detailViewTasks from './detailViewTasks'// 查看任务明细
import { getPartsInReviewTask } from '@/api/outstock/fhdb'
export default {
  name: 'ViewTasks',
  components: { detailViewTasks },
  data() {
    return {
      hasDialog: false,
      loading: false, // 列表懒加载
      tasksData: [], // 任务数据
      tasksDetailData: [], // 任务数据
      tablePage: {
        total: 0,
        pageNum: 1,
        pageSize: 100
      },
      tablePage1: {
        total: 0,
        pageNum: 1,
        pageSize: 100
      },
      outerVisible: false,
      innerVisible: false
    }
  },
  methods: {
    findList(){
      this.loading = true
      const { pageNum, pageSize } = this.tablePage
      const params = Object.assign({}, { pageNum, pageSize })
      // 查看任务
      getPartsInReviewTask(params).then(res => {
        this.loading = false
        const { code, msg, result } = res
        if (code === 0) {
          const { list, pageNum, total } = result
          // this.tablePage = { ...this.tablePage, pageNum, total }
          this.tablePage.pageNum = pageNum
          this.tablePage.total = total
          this.tasksData = list// 任务数据
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 分页
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNum = currentPage
      this.tablePage.pageSize = pageSize
      this.findList()//列表
    },
    // 关闭弹窗
    close() {
      this.$refs.dialogTableVisible.close()
    },
    // 禁止空白关闭弹窗
    closeDialog() {
      this.$emit('on-before-close')
    },
    // 打开弹窗
    open() {
      this.loading = true
      const { pageNum, pageSize } = this.tablePage
      const params = Object.assign({}, { pageNum, pageSize })
      // 查看任务
      getPartsInReviewTask(params).then(res => {
        this.loading = false
        const { code, msg, result } = res
        if (code === 0) {
          const { list, pageNum, total } = result
          this.tablePage = { ...this.tablePage, pageNum, total }
          this.tasksData = list// 任务数据
        } else {
          this.$message.error(msg)
        }
      })
      this.$refs.dialogTableVisible.open()
    },
    riewRowEvent(row) {
      this.outerVisible = true
      this.$refs.detailDialog.open(row)
    },
    // esc关闭弹窗后继续操作，快捷键不可操作
    closedetailDialog() {
      this.hasDialog = false
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
