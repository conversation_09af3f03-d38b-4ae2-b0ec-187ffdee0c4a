<template>
  <div>
    <xyy-dialog
      ref="detailTableDialog"
      title="任务单明细"
      width="700px"
      @on-close="closedetailDialog"
    >
      <!-- table 组件 -->
      <vxe-table
        ref="detailTable"
        highlight-current-row
        highlight-hover-row
        height="500"
        class="tasks-detail"
        :loading="loading"
        :data="tasksDetailData"
      >
        <vxe-table-column field="clientName" title="单位名称" width="150" />
        <vxe-table-column field="orgCode" title="机构编码" width="150" />
        <vxe-table-column field="productCode" title="商品编码" width="150" />
        <vxe-table-column field="productName" title="商品名称" width="150" />
        <vxe-table-column field="batchNumber" title="批号" width="150" />
        <vxe-table-column field="packingUnit" title="包装单位" width="150" />
        <vxe-table-column field="goodsAllocation" title="货位" width="150" />
        <vxe-table-column field="pickingNumber" title="拣货数量" width="150" />
        <vxe-table-column field="allocationCode" title="分配单号" width="150" />
        <vxe-table-column field="specifications" title="规格" width="150" />
        <vxe-table-column field="manufacturer" title="生产厂家" width="150" />
        <vxe-table-column field="reviewNumber" title="复核数量" width="150" />
        <vxe-table-column field="orderCode" title="出库单号" width="150" />
        <vxe-table-column field="erpOrderCode" title="销售单号" width="150" />
      </vxe-table>
    </xyy-dialog>
  </div>
</template>
<script>
import { getPartsInReviewTaskDetail } from "@/api/outstock/fhdb";
export default {
  name: "DetailViewTasks",
  data() {
    return {
      loading: false, // 列表懒加载
      tasksDetailData: [], // 任务数据Size: 100
    };
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$refs.detailTableDialog.close();
    },
    // 禁止空白关闭弹窗
    closedetailDialog() {
      this.$emit("on-before-close");
    },
    // 打开弹窗
    open(row) {
      this.loading = true;
      const orderCode = row.orderCode;
      const params = Object.assign({}, { orderCode });
      getPartsInReviewTaskDetail(params).then((res) => {
        this.loading = false;
        const { code, msg, result } = res;
        if (code === 0) {
          this.tasksDetailData = result; // 任务明细数据
        } else {
          this.$message.error(msg);
        }
      });
      this.$refs.detailTableDialog.open();
    },
  },
};
</script>
<style lang="scss" scoped></style>
