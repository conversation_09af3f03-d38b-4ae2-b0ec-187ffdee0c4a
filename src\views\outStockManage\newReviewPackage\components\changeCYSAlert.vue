<template>
  <el-dialog :title="'承运商更换'" :visible.sync="dialogVisible" width="400px">
    <div style="width:350px">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
        class="clearfix"
      >
        <el-col :lg="24" :md="24">
          <el-form-item label="单据编号" prop="code" style="height:50px">
            <el-input
              v-model="form.code"
              @keyup.enter.native="searchInput()"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="24" :md="24">
          <el-form-item label="承运商" prop="carrier" style="height:50px">
            <el-select v-model="form.carrier" @change="changecys">
              <el-option
                v-for="item in logisticsProvidersData"
                :key="item.logisticsProvidersCode"
                :label="item.logisticsProviders"
                :value="item.logisticsProvidersCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="24" :md="24">
          <el-form-item
            label="产品名称"
            prop="carrierProductCode"
            style="height:50px"
          >
            <el-select v-model="form.carrierProductCode">
              <el-option
                v-for="item in productData"
                :key="item.productCode + 10000"
                :label="item.productName"
                :value="item.productCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveFunction()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  logisticsProvidersQueryAll,
  logisticsProvidersQueryProduct,
  findCarrier
} from "@/api/baseBasic";
import {changeCarrier} from '@/api/outstock/fhdb';

export default {
  data() {
    return {
      dialogVisible: false,
      errmsg: "",
      logisticsProvidersData: [],
      productData: [],
      oldForm: {},
      form: {
        code: "",
        carrier: "",
        carrierName: "",
        carrierProductCode: undefined,
        oldCarrierName: "",
      },
      rules: {
        code: [
          { required: true, trigger: "blur", message: "请输入单号" },
        ],
        carrier: [
          { required: true, trigger: "change", message: "请选择承运商" },
        ],
        carrierProductCode: [
          {
            validator: (rule, value, callback) => {
              if (this.form.carrier !== "ZTO" && value === undefined) {
                callback(new Error("请选择产品"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    open(val) {
      this.dialogVisible = true;
      this.form = {
        code: '',
        carrier: "",
        carrierName: "",
        carrierProductCode: undefined,
        oldCarrierName: "",
      };
      this.errmsg = "";
      this.productData = [];
      this.logisticsProvidersData = [];
      this.apiLogisticsProvidersQueryAll();
      if (val) {
        this.apifindCarrier(val);
      }
    },
    searchInput() {
      //此处应有请求,
      this.apifindCarrier(this.form.code);
    },
    //物流商的options
    apiLogisticsProvidersQueryAll() {
      logisticsProvidersQueryAll().then((res) => {
        const { result, msg, code } = res;
        if (code === 0) {
          this.logisticsProvidersData = result;
        } else {
          this.$message.error(msg);
        }
      });
    },
    changecys(val) {
      this.form.carrierName = this.logisticsProvidersData.find((item) => {
        return this.form.carrier === item.logisticsProvidersCode;
      }).logisticsProviders;
      this.form.carrierProductCode = undefined;
      this.$refs.form.validate();
      this.apiLogisticsProvidersQueryProduct(val);
    },

    //产品名称
    apiLogisticsProvidersQueryProduct(val) {
      this.productData = []
      this.form.oldCarrierName = this.oldForm.carrierName;
      logisticsProvidersQueryProduct({ logisticsProvidersCode: val.logisticsProvidersCode }).then(
        (res) => {
          const { result, msg, code } = res;
          if (code === 0) {
            this.productData = result;
          } else {
            this.$message.error(msg);
          }
        }
      );
    },
    apifindCarrier(codes) {
      findCarrier({ code: codes }).then((res) => {
        const { result, msg, code } = res;
        if (code === 0) {
          this.oldForm = {
            code: codes,
            carrier: result.carrier,
            carrierName: result.carrierName,
            carrierProductCode: result.carrierProductCode,
          };
          this.form = {
            code: codes,
            carrier: result.carrier,
            carrierName: result.carrierName,
            carrierProductCode: result.carrierProductCode,
            oldCarrierName: "",
          };
          this.apiLogisticsProvidersQueryProduct(this.form.carrier);
        } else {
          this.errmsg = msg;
          this.$message.error(msg);
        }
      });
    },
    apiChangeCarrier() {
      let query = {
        code: this.form.code,
        carrier: this.form.carrier,
        carrierName: this.form.carrierName,
        carrierProductCode: this.form.carrierProductCode,
      };
      changeCarrier(query).then((res) => {
        const { result, msg, code } = res;
        if (code === 0) {
          this.$emit("back-data", query);
          this.dialogVisible = false;
          this.$message.success(msg);
        } else {
          this.$message.error(msg);
        }
      });
    },
    saveFunction() {
      if (this.form.code === "" || !this.form.code) {
        let msg = this.errmsg === "" ? "条码不存在任务" : this.errmsg;
        this.$message.error(msg);
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.apiChangeCarrier();
        }
      });
    },
  },
};
</script>
